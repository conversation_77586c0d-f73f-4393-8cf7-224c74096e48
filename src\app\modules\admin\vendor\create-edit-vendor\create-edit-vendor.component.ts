import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { CountryList } from 'app/core/common/country';
import { Util } from 'app/core/common/util';
import { Vendor } from 'app/core/models/vendor.model';
import { SharedModule } from 'app/shared/shared.module';
import {
  CommonService,
  FORM_CONTROL_TYPES,
  FormFactoryService,
  HTTP_STATUS,
  InvokeService,
  TABLE_ACTION_TYPES,
} from 'app/shared/tapas-ui';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { ReplaySubject, takeUntil } from 'rxjs';

import { VENDOR_FORM_MODEL } from './vendor-form.model';

@Component({
  selector: 'tps-create-edit-vendor',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './create-edit-vendor.component.html'
})
export class CreateEditVendorComponent implements OnInit, OnDestroy {
  public hdr: string = 'Create Vendor';
  public vendorForm: FormGroup;
  public fields: any[] = [];
  public rec: Vendor = new Vendor();
  isMobile: boolean = false;
  ctryList: CountryList;
  selectedCountryData: any = null;

  //constants
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  public TABLE_ACTION_TYPES = TABLE_ACTION_TYPES;
  public status: string;

  //unsubscribe
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);

  constructor(
    private _commonService: CommonService,
    private _dialogConfig: DynamicDialogConfig,
    public _dialogConfigRef: DynamicDialogRef,
    private _formFactoryService: FormFactoryService,
    private _invokeService: InvokeService,
  ) { }

  ngOnInit(): void {
    // Monitor screen size changes
    this._commonService.isMobile().subscribe(result => {
      this.isMobile = result.matches;
    });

    this.status = this._dialogConfig.data?.status;

    // Load record data first if available
    if (this._dialogConfig.data?.data) {
      this.rec = this._dialogConfig.data.data;
      console.log('ngOnInit - Loaded record:', this.rec);
    }

    this.buildForm();
    this.configureForm();
  }

  //build form
  private buildForm() {
    // Initialize country list
    this.ctryList = new CountryList();

    // Clone the form model to avoid modifying the original
    const formModel = Util.clone(VENDOR_FORM_MODEL);

    // Set country options from CountryList (already sorted in constructor)
    formModel.country.options = this.ctryList.countries;

    // Debug: Log the countries to see the order
    console.log('Country options:', this.ctryList.countries);

    // Add onChange handler for country field to update coordinates
    formModel.country.onChange = (event: any) => {
      this.onCountryChange(event.value);
    };

    const formGroupFields = this._formFactoryService.getFormControlsFields(formModel);
    this.fields = this._formFactoryService.getFieldsList(formModel);
    this.vendorForm = new FormGroup(formGroupFields);
  }

  //configure form
  private configureForm(): void {
    console.log('Configure form - Status:', this.status);
    console.log('Configure form - Record data:', this.rec);

    switch (this.status) {

      case TABLE_ACTION_TYPES.CREATE:
        // this.updateSelectedCountryData();
        break;
      case TABLE_ACTION_TYPES.EDIT:
        this.hdr = "Update Vendor";
        // Set country first if available
        if (this.rec?.country) {
          this.ctryList.setCountry(this.rec.country);
          // this.updateSelectedCountryData();
        }
        this.setDataToForm();
        // Ensure code field is editable in edit mode
        this.vendorForm.get('code')?.disable();
        break;
      case TABLE_ACTION_TYPES.VIEW:
        this.hdr = "View Vendor";
        // Set country first if available
        if (this.rec?.country) {
          this.ctryList.setCountry(this.rec.country);
          // this.updateSelectedCountryData();
        }
        this.setDataToForm();
        this.vendorForm.disable();
        break;
      default:
        break;
    }
  }

  // Handle country selection change
  public onCountryChange(selectedCountry: string): void {
    this.ctryList.setCountry(selectedCountry);
    // this.updateSelectedCountryData();
  }

  // Update selected country data for display
  // private updateSelectedCountryData(): void {
  //   const countryData = this.ctryList.getSelectedCountryData();
  //   if (countryData) {
  //     this.selectedCountryData = {
  //       name: countryData.name,
  //       latitude: countryData.latitude,
  //       longitude: countryData.longitude,
  //       code: countryData.code
  //     };
  //   } else {
  //     this.selectedCountryData = null;
  //   }
  // }

  private setDataToForm(): void {
    this.rec = this._dialogConfig.data?.data;
    console.log('setDataToForm - Record:', this.rec);
    console.log('setDataToForm - Country from record:', this.rec?.country);

    this.vendorForm = this._formFactoryService.setFormControlsValues(this.vendorForm, this.rec, this.fields);

    // Set the selected country in the CountryList
    if (this.rec.country) {
      console.log('Setting country:', this.rec.country);
      this.ctryList.setCountry(this.rec.country);
      this.updateSelectedCountryData();
      console.log('Selected country data after update:', this.selectedCountryData);
    }

    // Ensure code field is editable in edit mode (additional safety check)
    if (this.status === TABLE_ACTION_TYPES.EDIT) {
      this.vendorForm.get('code')?.enable();
    }
  }

  //submit form
  public onSubmit() {
    if (this.isMobile) {
      // Add any mobile-specific handling here
    }
    this.rec = this._formFactoryService.mapFormFieldsDataToModel(this.vendorForm, this.rec, this.fields);

    if (this.rec.uuid) {
      this._commonService.confirm(`Are you sure you want to update a vendor`, this.rec.fullName || this.rec.firstName)
        .pipe(takeUntil(this.$destroyed))
        .subscribe(response => {
          if (response) {
            APP_UI_CONFIG.administration.vendors.update.paramList.id = this.rec.uuid;
            this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.vendors.update, '', Util.clone(this.rec))
              .pipe(takeUntil(this.$destroyed))
              .subscribe({
                next: response => {
                  if (response.code == HTTP_STATUS.SUCCESS) {
                    this._commonService.success(`Vendor updated successfully`);
                    this.close(true);
                  } else if (response.code == 400) {
                    this._commonService.error(response.message);
                  }
                  else {
                    this._commonService.error('Failed to update the vendor details');
                  }
                }, error: error => {
                  this._commonService.handleError(error);
                }
              })
          }
        })
    } else {
      this._commonService.confirm(`Are you sure you want to create a vendor`, this.rec.fullName || this.rec.firstName)
        .pipe(takeUntil(this.$destroyed))
        .subscribe(response => {
          if (response) {
            this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.vendors.create, '', Util.clone(this.rec))
              .pipe(takeUntil(this.$destroyed))
              .subscribe({
                next: response => {
                  if (response.code == HTTP_STATUS.SUCCESS) {
                    this._commonService.success(`New vendor created successfully`);
                    this.close(true);
                  } else if (response.code == 400) {
                    this._commonService.error(response.message);
                  }
                  else {
                    this._commonService.error('Failed to create the vendor details');
                  }
                }, error: error => {
                  this._commonService.handleError(error);
                }
              })
          }
        })
    }
  }

  //close modal
  public close(status) {
    this._dialogConfigRef.close(status);
  }

  public ngOnDestroy(): void {
    this.$destroyed.next(null);
    this.$destroyed.complete();
  }
}
