export class Vendor {
    code: string;     // unique user given code for the vendor
    uuid?: any; // unique ID for the vendor
    userName?: string;
    firstName?: string;
    fullName?: string;
    contactNo?: string;
    email?: string;
    altemail?: string;
    addressLine1?: string;
    city?: string;
    state?: string;
    pincode?: string;
    country?: string;
    createdOn?: number;
    modifiedOn?: number;
    status?: number;
    isActive?: boolean;
    deleted?: number; // 0 = active, 1 = deleted/deactivated
};
